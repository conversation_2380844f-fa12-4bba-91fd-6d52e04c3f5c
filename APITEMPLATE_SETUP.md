# APItemplate.io Integration Setup

This document explains how to set up and use the APItemplate.io integration for contract generation in the InfluencerFlow application.

## Overview

The APItemplate integration allows you to:
- Create professional PDF contracts using pre-designed templates
- Automatically populate contract data from campaigns and influencers
- Store generated contracts in Supabase storage
- Download and view contracts from the dashboard

## Setup Instructions

### 1. APItemplate.io Account Setup

1. Sign up for an account at [APItemplate.io](https://apitemplate.io)
2. Create a new template or use an existing one
3. Note down your Template ID (you'll need this when creating contracts)
4. Get your API key from the APItemplate dashboard

### 2. Environment Variables

Add the following environment variables to your `.env` file:

```env
# APItemplate API Key (for client-side usage)
VITE_APITEMPLATE_API_KEY=your_apitemplate_api_key_here

# APItemplate API Key (for server-side Supabase functions)
APITEMPLATE_API_KEY=your_apitemplate_api_key_here
```

### 3. Supabase Setup

The integration uses a Supabase Edge Function located at:
`supabase/functions/create-apitemplate-contract/index.ts`

To deploy this function:

```bash
supabase functions deploy create-apitemplate-contract
```

Make sure to set the environment variable in Supabase:

```bash
supabase secrets set APITEMPLATE_API_KEY=your_apitemplate_api_key_here
```

### 4. Template Design

When designing your template in APItemplate.io, you can use the following data fields:

#### Campaign Data
- `campaignName` - Name of the campaign
- `brandName` - Brand name
- `campaignDescription` - Campaign description
- `campaignGoals` - Campaign goals
- `deliverables` - Campaign deliverables
- `timeline` - Campaign timeline
- `budget` - Campaign budget

#### Influencer Data
- `influencerName` - Influencer's name
- `influencerHandle` - Social media handle
- `influencerPlatform` - Platform (Instagram, YouTube, etc.)
- `influencerFollowers` - Follower count
- `influencerEngagementRate` - Engagement rate percentage

#### Contract Data
- `contractId` - Unique contract identifier
- `fee` - Contract fee amount
- `deadline` - Contract deadline
- `startDate` - Contract start date
- `endDate` - Contract end date
- `paymentTerms` - Payment terms
- `createdDate` - Contract creation date
- `brandRepresentative` - Brand representative name
- `specialInstructions` - Special instructions

## Usage

### Creating a Contract

1. Navigate to the Contracts section in the dashboard
2. Click "Create Contract"
3. Select a campaign from the dropdown
4. Select an influencer from that campaign
5. Fill in the contract details:
   - Fee amount
   - APItemplate Template ID
   - Start date (optional)
   - Deadline (required)
   - End date (optional)
   - Payment terms (optional)
   - Special instructions (optional)
6. Click "Create Contract"

The system will:
1. Send the data to APItemplate.io
2. Generate a PDF contract
3. Download and store the PDF in Supabase
4. Create a contract record in the database
5. Display the contract in the contracts list

### Viewing and Downloading Contracts

- **View**: Click the eye icon to open the contract PDF in a new tab
- **Download**: Click the download icon to download the contract PDF

## API Reference

### APItemplate Service

The `APItemplateService` class provides methods for:

- `createContract(payload)` - Create a contract using APItemplate API
- `downloadAndStorePDF(url, contractId, campaignId, influencerId)` - Download and store PDF
- `createContractPayload(templateId, campaign, influencer, contractData)` - Helper to create payload

### Supabase Edge Function

The `create-apitemplate-contract` function:

1. Authenticates the user
2. Fetches campaign and influencer data
3. Calls APItemplate API
4. Downloads and stores the generated PDF
5. Creates a contract record
6. Returns success/error response

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   - Ensure `APITEMPLATE_API_KEY` is set in Supabase secrets
   - Ensure `VITE_APITEMPLATE_API_KEY` is set in your `.env` file

2. **Template Not Found**
   - Verify the Template ID is correct
   - Ensure the template exists in your APItemplate account

3. **PDF Generation Failed**
   - Check that all required template fields are provided
   - Verify template design doesn't have syntax errors

4. **Storage Upload Failed**
   - Ensure Supabase storage bucket "contracts" exists
   - Check storage permissions and policies

### Error Logs

Check the following for debugging:
- Browser console for client-side errors
- Supabase Edge Function logs for server-side errors
- APItemplate API response for template/generation errors

## Security Notes

- API keys are stored securely in environment variables
- PDFs are stored in private Supabase storage with signed URLs
- User authentication is required for all contract operations
- Contract access is restricted to the creating user

## Support

For issues related to:
- APItemplate API: Contact APItemplate support
- Supabase integration: Check Supabase documentation
- Application bugs: Create an issue in the project repository
