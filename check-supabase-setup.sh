#!/bin/bash

echo "🔍 Checking Supabase Setup..."

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed"
    echo "Install with: npm install -g supabase"
    exit 1
else
    echo "✅ Supabase CLI is installed"
fi

# Check if we're logged in
echo "🔐 Checking login status..."
if supabase projects list &> /dev/null; then
    echo "✅ Logged in to Supabase"
    echo "📋 Available projects:"
    supabase projects list
else
    echo "❌ Not logged in to Supabase"
    echo "Login with: supabase login"
    exit 1
fi

# Check if project is linked
echo "🔗 Checking project link..."
if [ -f ".supabase/config.toml" ]; then
    echo "✅ Project configuration found"
    PROJECT_REF=$(grep 'project_id' .supabase/config.toml | cut -d'"' -f2)
    echo "📍 Project ID: $PROJECT_REF"
else
    echo "❌ No project linked"
    echo "Link with: supabase link --project-ref your-project-ref"
    exit 1
fi

# Check environment file
echo "📄 Checking environment variables..."
if [ -f ".env" ]; then
    echo "✅ .env file found"
    
    if grep -q "VITE_SUPABASE_URL" .env; then
        echo "✅ VITE_SUPABASE_URL is set"
    else
        echo "❌ VITE_SUPABASE_URL is missing"
    fi
    
    if grep -q "VITE_SUPABASE_ANON_KEY" .env; then
        echo "✅ VITE_SUPABASE_ANON_KEY is set"
    else
        echo "❌ VITE_SUPABASE_ANON_KEY is missing"
    fi
    
    if grep -q "VITE_APITEMPLATE_API_KEY" .env; then
        echo "✅ VITE_APITEMPLATE_API_KEY is set"
    else
        echo "⚠️  VITE_APITEMPLATE_API_KEY is missing (optional for testing)"
    fi
else
    echo "❌ .env file not found"
fi

# Try to list functions
echo "🔧 Checking Edge Functions..."
if supabase functions list &> /dev/null; then
    echo "✅ Can access Edge Functions"
    echo "📋 Deployed functions:"
    supabase functions list
else
    echo "❌ Cannot access Edge Functions"
    echo "This might be normal if no functions are deployed yet"
fi

echo ""
echo "🚀 Next steps to deploy functions:"
echo "1. Make sure you're in the project directory"
echo "2. Run: supabase functions deploy test-contract"
echo "3. Run: supabase functions deploy create-apitemplate-contract"
echo "4. Set secrets: supabase secrets set APITEMPLATE_API_KEY=your_key"
echo ""
echo "🧪 Test the setup in your application using the Supabase Checker component"
