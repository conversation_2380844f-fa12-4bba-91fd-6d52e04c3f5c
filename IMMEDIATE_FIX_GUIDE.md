# 🚨 Immediate Fix Guide for Edge Function Issues

## Current Issues Identified:
1. ❌ Database Connection Failed - SQL syntax error
2. ❌ Edge Functions Failed - Not deployed or not accessible
3. ❌ Supabase configuration issues

## 🔧 Quick Fix Steps

### Step 1: Test the New Diagnostic Tools

1. **Go to Contracts → APItemplate Contracts → Create Contract**
2. **Use the new diagnostic tools in this order:**
   - **Supabase Configuration Checker** (orange box at top)
   - **Edge Function Tester** (blue box)
   - **Direct Contract Creator** (green box at bottom)

### Step 2: Check Supabase Configuration

Run the configuration checker script:
```bash
./check-supabase-setup.sh
```

This will tell you:
- ✅ If Supabase CLI is installed
- ✅ If you're logged in
- ✅ If project is linked
- ✅ If environment variables are set

### Step 3: Fix Environment Variables

Check your `.env` file has these variables:
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_APITEMPLATE_API_KEY="7c22MzExODc6MjgzNjA6ajNxb1MzMlFFUUt2c3NicQ="
VITE_APITEMPLATE_TEMPLATE_ID=your_template_id_here
```

### Step 4: Use Direct Contract Creation (Immediate Workaround)

**This bypasses Edge Functions entirely:**

1. Go to Create Contract dialog
2. Select campaign and influencer
3. Fill in deal amount and deadline
4. Scroll down to the **green "Direct Contract Creation"** box
5. Click **"Create Contract Directly"**

This will:
- ✅ Create contract in database immediately
- ✅ Skip Edge Function requirements
- ✅ Work even without APItemplate setup
- ✅ Allow you to test the full flow

### Step 5: Deploy Edge Functions (For Full Functionality)

If you want the full APItemplate integration:

```bash
# Check if you're logged in to Supabase
supabase login

# Link to your project (replace with your actual project ref)
supabase link --project-ref your-project-ref

# Deploy the functions
supabase functions deploy test-contract
supabase functions deploy create-apitemplate-contract

# Set the API key
supabase secrets set APITEMPLATE_API_KEY="7c22MzExODc6MjgzNjA6ajNxb1MzMlFFUUt2c3NicQ="
```

## 🎯 Expected Results

### ✅ Working State:
- **Supabase Checker**: All green checkmarks
- **Database Test**: Success
- **Direct Contract Creation**: Works immediately
- **Edge Function Test**: Success (after deployment)

### ⚠️ Partial Working State:
- **Database**: Works
- **Direct Contract**: Works
- **Edge Functions**: May fail (but you can still create contracts)

### ❌ Not Working State:
- **Database**: Fails (check environment variables)
- **All tests**: Fail (check Supabase configuration)

## 🚀 Immediate Actions You Can Take

### Option 1: Use Direct Contract Creation (Recommended for immediate testing)
1. Open Create Contract dialog
2. Select campaign and influencer
3. Use the green "Direct Contract Creator" box
4. This will work immediately without any setup

### Option 2: Fix Supabase Configuration
1. Run `./check-supabase-setup.sh`
2. Follow the instructions it provides
3. Fix any missing environment variables
4. Test with Supabase Checker

### Option 3: Deploy Edge Functions
1. Ensure Supabase CLI is set up
2. Deploy functions using the commands above
3. Test with Edge Function Tester

## 🔍 Debugging Information

### Check Browser Console
Look for these messages:
- ✅ "Database accessible" = Good
- ❌ "Failed to parse select parameter" = Environment issue
- ❌ "Failed to send request" = Edge Function not deployed

### Check Network Tab
- Look for requests to `/functions/v1/test-contract`
- 404 = Function not deployed
- 500 = Function deployed but has errors
- Network error = Configuration issue

## 📋 Priority Order

1. **First**: Use Direct Contract Creation to verify basic functionality
2. **Second**: Fix Supabase configuration using the checker tools
3. **Third**: Deploy Edge Functions for full APItemplate integration

## 🎉 Success Indicators

You'll know it's working when:
- ✅ Supabase Checker shows all green
- ✅ Direct Contract Creation works
- ✅ Contracts appear in the contracts list
- ✅ No errors in browser console

The Direct Contract Creator is your immediate solution - it will create contracts right now without any additional setup required!
