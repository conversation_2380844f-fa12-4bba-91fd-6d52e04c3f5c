#!/bin/bash

echo "🚀 Deploying Supabase Edge Functions..."

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Deploy test function first
echo "📦 Deploying test-contract function..."
supabase functions deploy test-contract

if [ $? -eq 0 ]; then
    echo "✅ test-contract function deployed successfully"
else
    echo "❌ Failed to deploy test-contract function"
    exit 1
fi

# Deploy main contract function
echo "📦 Deploying create-apitemplate-contract function..."
supabase functions deploy create-apitemplate-contract

if [ $? -eq 0 ]; then
    echo "✅ create-apitemplate-contract function deployed successfully"
else
    echo "❌ Failed to deploy create-apitemplate-contract function"
    exit 1
fi

# Set environment variables
echo "🔧 Setting environment variables..."

# Check if API key is set
if [ -z "$APITEMPLATE_API_KEY" ]; then
    echo "⚠️  APITEMPLATE_API_KEY environment variable not set"
    echo "Please set it manually with:"
    echo "supabase secrets set APITEMPLATE_API_KEY=your_api_key_here"
else
    supabase secrets set APITEMPLATE_API_KEY="$APITEMPLATE_API_KEY"
    echo "✅ APITEMPLATE_API_KEY set"
fi

# Check if template ID is set
if [ -z "$APITEMPLATE_TEMPLATE_ID" ]; then
    echo "⚠️  APITEMPLATE_TEMPLATE_ID environment variable not set"
    echo "Please set it manually with:"
    echo "supabase secrets set APITEMPLATE_TEMPLATE_ID=your_template_id_here"
else
    supabase secrets set APITEMPLATE_TEMPLATE_ID="$APITEMPLATE_TEMPLATE_ID"
    echo "✅ APITEMPLATE_TEMPLATE_ID set"
fi

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "📋 Next steps:"
echo "1. Test the functions in your application"
echo "2. Check the Supabase dashboard for function logs"
echo "3. If you haven't set the API keys, run:"
echo "   supabase secrets set APITEMPLATE_API_KEY=your_api_key"
echo "   supabase secrets set APITEMPLATE_TEMPLATE_ID=your_template_id"
echo ""
echo "🔍 To view function logs:"
echo "   supabase functions logs test-contract"
echo "   supabase functions logs create-apitemplate-contract"
