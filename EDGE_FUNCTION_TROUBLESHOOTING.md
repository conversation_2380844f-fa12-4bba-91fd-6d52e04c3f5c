# Edge Function Troubleshooting Guide

## 🚨 "Failed to send a request to the Edge Function" Error

This error typically occurs when:
1. Edge Functions are not deployed
2. Supabase project is not properly configured
3. Network connectivity issues
4. Authentication problems

## 🔧 Step-by-Step Fix

### 1. Check Supabase CLI Installation

```bash
# Install Supabase CLI if not installed
npm install -g supabase

# Verify installation
supabase --version
```

### 2. Login to Supabase

```bash
# Login to your Supabase account
supabase login

# Link to your project (replace with your project reference)
supabase link --project-ref your-project-ref
```

### 3. Deploy Edge Functions

```bash
# Make the deployment script executable
chmod +x deploy-functions.sh

# Run the deployment script
./deploy-functions.sh

# Or deploy manually:
supabase functions deploy test-contract
supabase functions deploy create-apitemplate-contract
```

### 4. Set Environment Variables

```bash
# Set APItemplate API key
supabase secrets set APITEMPLATE_API_KEY="your_api_key_here"

# Set template ID
supabase secrets set APITEMPLATE_TEMPLATE_ID="your_template_id_here"

# Verify secrets are set
supabase secrets list
```

### 5. Test Edge Functions

Use the Edge Function Tester component in the application:

1. Go to Contracts → APItemplate Contracts
2. Click "Create Contract"
3. Use the Edge Function Tester at the top
4. Test in this order:
   - Test Database (should work)
   - Test Simple Function (should work)
   - Test Main Function (may fail if APItemplate not configured)

## 🐛 Common Issues and Solutions

### Issue 1: "Function not found"
**Solution:** Deploy the functions
```bash
supabase functions deploy test-contract
supabase functions deploy create-apitemplate-contract
```

### Issue 2: "Invalid JWT" or Authentication errors
**Solution:** Check your Supabase configuration
```bash
# Check if you're linked to the correct project
supabase status

# Re-link if necessary
supabase link --project-ref your-project-ref
```

### Issue 3: "APItemplate API key not configured"
**Solution:** Set the API key
```bash
supabase secrets set APITEMPLATE_API_KEY="your_actual_api_key"
```

### Issue 4: Functions deploy but don't work
**Solution:** Check function logs
```bash
# View logs for test function
supabase functions logs test-contract

# View logs for main function
supabase functions logs create-apitemplate-contract
```

## 🧪 Testing Strategy

### Phase 1: Basic Connectivity
1. Test database connection
2. Test simple Edge Function (test-contract)
3. Verify function logs show no errors

### Phase 2: Contract Creation
1. Test main Edge Function without APItemplate
2. Check if contract records are created in database
3. Verify error handling works

### Phase 3: APItemplate Integration
1. Configure APItemplate API key
2. Set template ID
3. Test full contract creation with PDF generation

## 📋 Verification Checklist

- [ ] Supabase CLI installed and logged in
- [ ] Project linked correctly
- [ ] Edge Functions deployed successfully
- [ ] Environment variables set
- [ ] Database connection works
- [ ] Simple Edge Function works
- [ ] Main Edge Function works (at least without APItemplate)
- [ ] APItemplate API key configured (optional for basic testing)
- [ ] Template ID configured (optional for basic testing)

## 🔍 Debug Information

### Check Supabase Project Settings
1. Go to Supabase Dashboard
2. Navigate to Settings → API
3. Verify Project URL and API keys
4. Check if Edge Functions are listed

### Check Function Logs
```bash
# Real-time logs
supabase functions logs test-contract --follow

# Recent logs
supabase functions logs create-apitemplate-contract
```

### Check Network
```bash
# Test if you can reach Supabase
curl -I https://your-project-ref.supabase.co

# Test Edge Function directly (replace with your project URL)
curl -X POST https://your-project-ref.supabase.co/functions/v1/test-contract \
  -H "Authorization: Bearer your-anon-key" \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

## 🚀 Quick Fix Commands

```bash
# Complete setup in one go
supabase login
supabase link --project-ref your-project-ref
supabase functions deploy test-contract
supabase functions deploy create-apitemplate-contract
supabase secrets set APITEMPLATE_API_KEY="your_api_key"
supabase secrets set APITEMPLATE_TEMPLATE_ID="your_template_id"
```

## 📞 Getting Help

If you're still having issues:

1. Check the browser console for detailed error messages
2. Check Supabase function logs
3. Verify your project configuration in Supabase dashboard
4. Test with the Edge Function Tester component
5. Try the simple test function first before the main function

## 🎯 Expected Behavior

**Working correctly:**
- Edge Function Tester shows green checkmarks
- Contract creation succeeds (even without PDF if APItemplate not configured)
- No errors in browser console
- Function logs show successful execution

**Needs attention:**
- Red X marks in Edge Function Tester
- "Failed to send request" errors
- Empty function logs
- Authentication errors
