import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface APItemplatePayload {
  template_id: string;
  data: Record<string, any>;
  export_type?: 'pdf' | 'png' | 'jpg';
  expiration?: number;
  output_format?: 'url' | 'base64';
}

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Edge Function called with method:", req.method);

    // Authentication (simplified for testing)
    let userId = crypto.randomUUID(); // Generate UUID for anonymous users

    try {
      const authClient = createClient(
        Deno.env.get("SUPABASE_URL")!,
        Deno.env.get("SUPABASE_ANON_KEY")!,
        {
          global: {
            headers: {
              Authorization: req.headers.get("Authorization") || "",
            },
          },
        }
      );

      const {
        data: { user },
        error: authError,
      } = await authClient.auth.getUser();

      if (user) {
        userId = user.id;
      }
    } catch (authError) {
      console.log("Auth failed, using anonymous user:", authError);
    }

    console.log("User ID:", userId);

    // Service client for database operations
    const serviceClient = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
    );

    // Parse request body
    const {
      campaignId,
      influencerId,
      fee,
      deadline,
      startDate,
      endDate,
      paymentTerms,
      specialInstructions
    } = await req.json();

    // Validate required UUIDs
    if (!campaignId || !influencerId) {
      throw new Error("Campaign ID and Influencer ID are required");
    }

    // Get template ID from environment (optional for testing)
    const templateId = Deno.env.get("APITEMPLATE_TEMPLATE_ID") || "test-template";

    console.log("Creating contract with:", {
      campaignId,
      influencerId,
      templateId,
      fee,
      deadline
    });

    // Fetch campaign data
    const { data: campaign, error: campaignError } = await serviceClient
      .from("campaigns")
      .select("*")
      .eq("id", campaignId)
      .single();

    if (campaignError || !campaign) {
      console.error("Campaign error:", campaignError);
      throw new Error(`Campaign not found: ${campaignError?.message || 'Unknown error'}`);
    }

    // Fetch influencer data
    const { data: influencer, error: influencerError } = await serviceClient
      .from("influencers")
      .select("*")
      .eq("id", influencerId)
      .single();

    if (influencerError || !influencer) {
      console.error("Influencer error:", influencerError);
      throw new Error(`Influencer not found: ${influencerError?.message || 'Unknown error'}`);
    }

    // Generate contract ID
    const contractId = `CTR_${Date.now()}_${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

    // Prepare APItemplate payload
    const apitemplatePayload: APItemplatePayload = {
      template_id: templateId,
      data: {
        // Campaign data
        campaignName: campaign.name || 'N/A',
        brandName: campaign.brand || 'N/A',
        campaignDescription: campaign.description || '',
        campaignGoals: campaign.goals || '',
        deliverables: campaign.deliverables || '',
        timeline: campaign.timeline || '',
        budget: campaign.budget || 0,

        // Influencer data
        influencerName: influencer.name || 'N/A',
        influencerHandle: influencer.handle || 'N/A',
        influencerPlatform: influencer.platform || 'N/A',
        influencerFollowers: influencer.followers_count || 0,
        influencerEngagementRate: influencer.engagement_rate || 0,

        // Contract specific data
        contractId,
        fee: fee || 0,
        deadline: deadline || 'TBD',
        startDate: startDate || new Date().toISOString().split('T')[0],
        endDate: endDate || deadline || 'TBD',
        paymentTerms: paymentTerms || 'Payment due within 30 days of deliverable completion',

        // Additional fields
        createdDate: new Date().toLocaleDateString(),
        brandRepresentative: 'Brand Representative',
        specialInstructions: specialInstructions || '',
      },
      export_type: 'pdf',
      output_format: 'url',
      expiration: 3600, // 1 hour expiration
    };

    // Call APItemplate API (with fallback)
    const apitemplateApiKey = Deno.env.get("APITEMPLATE_API_KEY");
    let storagePath = null;
    let apitemplateResult = { transaction_ref: 'test-transaction' };

    if (apitemplateApiKey && apitemplateApiKey !== "your_apitemplate_api_key_here") {
      console.log("Using APItemplate API...");

      try {
        const apitemplateResponse = await fetch("https://rest.apitemplate.io/v2/create-pdf", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-KEY": apitemplateApiKey,
          },
          body: JSON.stringify(apitemplatePayload),
        });

        if (!apitemplateResponse.ok) {
          const errorText = await apitemplateResponse.text();
          console.error("APItemplate API error:", errorText);
          throw new Error(`APItemplate API error: ${apitemplateResponse.status} ${apitemplateResponse.statusText}`);
        }

        apitemplateResult = await apitemplateResponse.json();

        if (apitemplateResult.status !== 'success' || !apitemplateResult.download_url) {
          console.error("APItemplate result error:", apitemplateResult);
          throw new Error("Failed to generate PDF with APItemplate");
        }

        // Download and store PDF
        const pdfResponse = await fetch(apitemplateResult.download_url);
        if (!pdfResponse.ok) {
          throw new Error("Failed to download PDF from APItemplate");
        }

        const pdfBlob = await pdfResponse.blob();
        const pdfBuffer = await pdfBlob.arrayBuffer();

        // Generate storage path
        const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
        storagePath = `contracts/${campaignId}/${influencerId}_${contractId}_${timestamp}.pdf`;

        // Upload to Supabase storage
        const { error: uploadError } = await serviceClient.storage
          .from("contracts")
          .upload(storagePath, pdfBuffer, {
            contentType: "application/pdf",
            upsert: false,
          });

        if (uploadError) {
          console.error("Upload error:", uploadError);
          throw new Error("Failed to upload PDF to storage");
        }

        console.log("PDF uploaded successfully to:", storagePath);
      } catch (error) {
        console.error("APItemplate failed, continuing without PDF:", error);
        storagePath = null;
      }
    } else {
      console.log("APItemplate API key not configured, skipping PDF generation");
    }

    // Create contract record
    const contractRow = {
      brand_user_id: userId,
      campaign_id: campaignId,
      influencer_id: influencerId,
      template_id: templateId,
      pdf_url: storagePath,
      status: "draft",
      contract_data: {
        fee: fee || 0,
        deadline: deadline || 'TBD',
        startDate: startDate || new Date().toISOString().split('T')[0],
        endDate: endDate || deadline || 'TBD',
        paymentTerms: paymentTerms || 'Payment due within 30 days',
        specialInstructions: specialInstructions || '',
        contractId,
        apitemplate_transaction_ref: apitemplateResult.transaction_ref,
        generated_at: new Date().toISOString(),
        creation_method: 'apitemplate-function',
      },
    };

    const { data: inserted, error: contractError } = await serviceClient
      .from("contracts")
      .insert(contractRow)
      .select("*")
      .single();

    if (contractError || !inserted) {
      console.error("Contract insert error:", contractError);
      throw new Error(`Failed to create contract record: ${contractError.message}`);
    }

    // Create signed URL for immediate download
    let signedUrl = null;
    if (storagePath) {
      const { data: signedData, error: signedError } = await serviceClient.storage
        .from("contracts")
        .createSignedUrl(storagePath, 3600); // 1 hour expiration

      if (!signedError) {
        signedUrl = signedData?.signedUrl;
      }
    }

    console.log("Contract created successfully:", inserted.id);

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        contract: inserted,
        downloadUrl: signedUrl,
        apitemplate_transaction_ref: apitemplateResult.transaction_ref,
        message: storagePath ? "Contract PDF generated successfully with APItemplate!" : "Contract created successfully (no PDF generated)",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );

  } catch (error) {
    console.error("Error in create-apitemplate-contract function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        details: error.stack || 'No stack trace available'
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
