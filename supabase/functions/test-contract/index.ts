import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Test contract function called");
    
    // Parse request body
    const body = await req.json();
    console.log("Request body:", body);

    // Create service client
    const serviceClient = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
    );

    // Generate a simple contract ID
    const contractId = `TEST_${Date.now()}`;

    // Create a basic contract record
    const contractRow = {
      brand_user_id: 'test-user',
      campaign_id: body.campaignId || 'test-campaign',
      influencer_id: body.influencerId || 'test-influencer',
      template_id: 'test-template',
      pdf_url: null,
      status: "draft",
      contract_data: {
        fee: body.fee || 0,
        deadline: body.deadline || new Date().toISOString().split('T')[0],
        startDate: body.startDate || new Date().toISOString().split('T')[0],
        endDate: body.endDate || new Date().toISOString().split('T')[0],
        paymentTerms: body.paymentTerms || 'Test payment terms',
        specialInstructions: body.specialInstructions || '',
        contractId,
        generated_at: new Date().toISOString(),
      },
    };

    console.log("Inserting contract:", contractRow);

    const { data: inserted, error: contractError } = await serviceClient
      .from("contracts")
      .insert(contractRow)
      .select("*")
      .single();

    if (contractError) {
      console.error("Contract insert error:", contractError);
      throw new Error(`Failed to create contract record: ${contractError.message}`);
    }

    console.log("Contract created successfully:", inserted.id);

    return new Response(
      JSON.stringify({
        success: true,
        contract: inserted,
        message: "Test contract created successfully!",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );

  } catch (error) {
    console.error("Error in test-contract function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
